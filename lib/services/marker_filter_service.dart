import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import '../models/unified_marker.dart';
import '../config/filter_config.dart';

/// 过滤结果
class FilterResult {
  /// 过滤后的标记列表
  final List<UnifiedMarker> filteredMarkers;

  /// 过滤统计信息
  final FilterStats stats;

  const FilterResult({required this.filteredMarkers, required this.stats});

  @override
  String toString() {
    return 'FilterResult(filtered: ${filteredMarkers.length}, stats: $stats)';
  }
}

/// 过滤统计信息
class FilterStats {
  /// 原始标记数量
  final int originalCount;

  /// 过滤后数量
  final int filteredCount;

  /// 各种过滤条件的过滤数量
  final Map<String, int> filterBreakdown;

  /// 过滤耗时（毫秒）
  final int processingTimeMs;

  const FilterStats({
    required this.originalCount,
    required this.filteredCount,
    required this.filterBreakdown,
    required this.processingTimeMs,
  });

  /// 过滤率
  double get filterRatio =>
      originalCount > 0 ? filteredCount / originalCount : 0.0;

  @override
  String toString() {
    return 'FilterStats(${originalCount} -> ${filteredCount}, ${(filterRatio * 100).toStringAsFixed(1)}%, ${processingTimeMs}ms)';
  }
}

/// 标记过滤服务
///
/// 实现各种过滤条件的检查逻辑
/// 支持钓点和活动的统一过滤接口
/// 提供过滤性能优化
class MarkerFilterService {
  /// 当前用户ID（用于个人关联过滤）
  String? _currentUserId;

  /// 当前用户位置（用于距离过滤）
  LatLng? _currentLocation;

  /// 过滤缓存（避免重复计算）
  final Map<String, bool> _filterCache = {};

  /// 缓存过期时间
  final Duration _cacheExpiry = const Duration(minutes: 5);

  /// 上次清理缓存的时间
  DateTime _lastCacheCleanup = DateTime.now();

  /// 设置当前用户ID
  void setCurrentUserId(String? userId) {
    if (_currentUserId != userId) {
      _currentUserId = userId;
      _clearFilterCache(); // 用户变更时清理缓存
    }
  }

  /// 设置当前用户位置
  void setCurrentLocation(LatLng? location) {
    _currentLocation = location;
    // 位置变更时不清理缓存，因为位置可能频繁变化
  }

  // ==================== 主要过滤接口 ====================

  /// 过滤标记列表
  FilterResult filterMarkers(List<UnifiedMarker> markers, FilterConfig config) {
    final stopwatch = Stopwatch()..start();

    debugPrint('🔍 [标记过滤] 开始过滤 ${markers.length} 个标记');

    // 定期清理过滤缓存
    _cleanupCacheIfNeeded();

    final filterBreakdown = <String, int>{};
    var currentMarkers = List<UnifiedMarker>.from(markers);

    // 按顺序应用各种过滤条件
    currentMarkers = _applyTypeFilter(currentMarkers, config, filterBreakdown);
    currentMarkers = _applyBasicFilters(
      currentMarkers,
      config,
      filterBreakdown,
    );
    currentMarkers = _applySocialFilters(
      currentMarkers,
      config,
      filterBreakdown,
    );
    currentMarkers = _applyPersonalFilters(
      currentMarkers,
      config,
      filterBreakdown,
    );
    currentMarkers = _applyDistanceFilter(
      currentMarkers,
      config,
      filterBreakdown,
    );
    currentMarkers = _applyDisplayLimitFilter(
      currentMarkers,
      config,
      filterBreakdown,
    );

    stopwatch.stop();

    final stats = FilterStats(
      originalCount: markers.length,
      filteredCount: currentMarkers.length,
      filterBreakdown: filterBreakdown,
      processingTimeMs: stopwatch.elapsedMilliseconds,
    );

    debugPrint('🔍 [标记过滤] 过滤完成: $stats');

    return FilterResult(filteredMarkers: currentMarkers, stats: stats);
  }

  /// 检查单个标记是否通过过滤
  bool checkMarkerPassesFilter(UnifiedMarker marker, FilterConfig config) {
    // 使用缓存提高性能
    final cacheKey = '${marker.id}_${config.hashCode}';
    final cachedResult = _filterCache[cacheKey];
    if (cachedResult != null) {
      return cachedResult;
    }

    final result = _checkMarkerInternal(marker, config);
    _filterCache[cacheKey] = result;

    return result;
  }

  // ==================== 具体过滤实现 ====================

  /// 应用类型过滤
  List<UnifiedMarker> _applyTypeFilter(
    List<UnifiedMarker> markers,
    FilterConfig config,
    Map<String, int> breakdown,
  ) {
    if (config.enabledTypes.length == MarkerType.values.length) {
      return markers; // 所有类型都启用，跳过过滤
    }

    final originalCount = markers.length;
    final filtered =
        markers.where((marker) {
          if (!config.enabledTypes.contains(marker.type)) {
            return false;
          }

          // 检查具体类型过滤
          if (marker is SpotMarker && config.enabledSpotTypes.isNotEmpty) {
            return config.enabledSpotTypes.contains(marker.spotType);
          }

          if (marker is ActivityMarker &&
              config.enabledActivityTypes.isNotEmpty) {
            return config.enabledActivityTypes.contains(marker.activityType);
          }

          return true;
        }).toList();

    breakdown['type_filter'] = originalCount - filtered.length;
    return filtered;
  }

  /// 应用基础过滤条件
  List<UnifiedMarker> _applyBasicFilters(
    List<UnifiedMarker> markers,
    FilterConfig config,
    Map<String, int> breakdown,
  ) {
    final originalCount = markers.length;
    final filtered =
        markers.where((marker) {
          // 过期检查
          if (!config.showExpired && marker.isExpired) {
            return false;
          }

          // 举报检查
          if (marker.isReported) {
            return false;
          }

          // 钓点特有的基础过滤
          if (marker is SpotMarker) {
            if (config.requireOnSite && !marker.isOnSite) {
              return false;
            }

            if (config.requireRealPhotos && !marker.hasRealPhotos) {
              return false;
            }

            if (config.requirePhotos && !marker.hasPhotos) {
              return false;
            }
          }

          // 活动特有的基础过滤
          if (marker is ActivityMarker) {
            // 检查活动是否已开始但未结束
            final now = DateTime.now();
            if (marker.startTime.isBefore(
              now.subtract(const Duration(hours: 24)),
            )) {
              return false; // 活动已结束超过24小时
            }
          }

          return true;
        }).toList();

    breakdown['basic_filter'] = originalCount - filtered.length;
    return filtered;
  }

  /// 应用社交过滤条件
  List<UnifiedMarker> _applySocialFilters(
    List<UnifiedMarker> markers,
    FilterConfig config,
    Map<String, int> breakdown,
  ) {
    final originalCount = markers.length;
    final filtered =
        markers.where((marker) {
          // 收藏过滤
          if (config.onlyFavorites && !marker.isFavorited) {
            return false;
          }

          // 钓点的社交过滤
          if (marker is SpotMarker) {
            if (config.requirePositiveLikes && marker.likesCount <= 0) {
              return false;
            }

            if (marker.likesCount < config.minLikesCount) {
              return false;
            }
          }

          // 活动的社交过滤
          if (marker is ActivityMarker) {
            // 检查参与度
            if (config.requirePositiveLikes &&
                marker.currentParticipants == 0) {
              return false;
            }
          }

          return true;
        }).toList();

    breakdown['social_filter'] = originalCount - filtered.length;
    return filtered;
  }

  /// 应用个人关联过滤条件
  List<UnifiedMarker> _applyPersonalFilters(
    List<UnifiedMarker> markers,
    FilterConfig config,
    Map<String, int> breakdown,
  ) {
    final originalCount = markers.length;
    final filtered =
        markers.where((marker) {
          // 我的钓点过滤
          if (config.onlyMySpots && marker is SpotMarker && !marker.isMine) {
            return false;
          }

          // 我的活动过滤
          if (config.onlyMyActivities &&
              marker is ActivityMarker &&
              !marker.isMyActivity) {
            return false;
          }

          // 已加入活动过滤
          if (config.onlyJoinedActivities &&
              marker is ActivityMarker &&
              !marker.isJoined) {
            return false;
          }

          return true;
        }).toList();

    breakdown['personal_filter'] = originalCount - filtered.length;
    return filtered;
  }

  /// 应用距离过滤
  List<UnifiedMarker> _applyDistanceFilter(
    List<UnifiedMarker> markers,
    FilterConfig config,
    Map<String, int> breakdown,
  ) {
    if (config.maxDisplayDistance == null || _currentLocation == null) {
      breakdown['distance_filter'] = 0;
      return markers;
    }

    final originalCount = markers.length;
    const Distance distance = Distance();

    final filtered =
        markers.where((marker) {
          final distanceKm = distance.as(
            LengthUnit.Kilometer,
            _currentLocation!,
            marker.location,
          );

          return distanceKm <= config.maxDisplayDistance!;
        }).toList();

    breakdown['distance_filter'] = originalCount - filtered.length;
    return filtered;
  }

  /// 应用显示数量限制
  List<UnifiedMarker> _applyDisplayLimitFilter(
    List<UnifiedMarker> markers,
    FilterConfig config,
    Map<String, int> breakdown,
  ) {
    if (markers.length <= config.maxDisplayCount) {
      breakdown['limit_filter'] = 0;
      return markers;
    }

    // 这里应该配合排序服务使用，取前N个最重要的标记
    // 暂时简单截取前N个
    final limited = markers.take(config.maxDisplayCount).toList();
    breakdown['limit_filter'] = markers.length - limited.length;

    return limited;
  }

  // ==================== 内部检查方法 ====================

  /// 内部标记检查方法
  bool _checkMarkerInternal(UnifiedMarker marker, FilterConfig config) {
    // 类型检查
    if (!config.enabledTypes.contains(marker.type)) {
      return false;
    }

    // 基础条件检查
    if (!config.showExpired && marker.isExpired) {
      return false;
    }

    if (marker.isReported) {
      return false;
    }

    // 收藏检查
    if (config.onlyFavorites && !marker.isFavorited) {
      return false;
    }

    // 钓点特有检查
    if (marker is SpotMarker) {
      if (config.requireOnSite && !marker.isOnSite) {
        return false;
      }

      if (config.requireRealPhotos && !marker.hasRealPhotos) {
        return false;
      }

      if (config.requirePhotos && !marker.hasPhotos) {
        return false;
      }

      if (config.requirePositiveLikes && marker.likesCount <= 0) {
        return false;
      }

      if (marker.likesCount < config.minLikesCount) {
        return false;
      }

      if (config.onlyMySpots && !marker.isMine) {
        return false;
      }

      if (config.enabledSpotTypes.isNotEmpty &&
          !config.enabledSpotTypes.contains(marker.spotType)) {
        return false;
      }
    }

    // 活动特有检查
    if (marker is ActivityMarker) {
      if (config.onlyMyActivities && !marker.isMyActivity) {
        return false;
      }

      if (config.onlyJoinedActivities && !marker.isJoined) {
        return false;
      }

      if (config.enabledActivityTypes.isNotEmpty &&
          !config.enabledActivityTypes.contains(marker.activityType)) {
        return false;
      }

      // 检查活动时间
      final now = DateTime.now();
      if (marker.startTime.isBefore(now.subtract(const Duration(hours: 24)))) {
        return false;
      }
    }

    // 距离检查
    if (config.maxDisplayDistance != null && _currentLocation != null) {
      const Distance distance = Distance();
      final distanceKm = distance.as(
        LengthUnit.Kilometer,
        _currentLocation!,
        marker.location,
      );

      if (distanceKm > config.maxDisplayDistance!) {
        return false;
      }
    }

    return true;
  }

  // ==================== 缓存管理 ====================

  /// 清理过滤缓存
  void _clearFilterCache() {
    _filterCache.clear();
    debugPrint('🧹 [过滤缓存] 缓存已清理');
  }

  /// 定期清理缓存
  void _cleanupCacheIfNeeded() {
    final now = DateTime.now();
    if (now.difference(_lastCacheCleanup) > _cacheExpiry) {
      _clearFilterCache();
      _lastCacheCleanup = now;
    }
  }

  // ==================== 统计和调试 ====================

  /// 获取过滤服务统计信息
  Map<String, dynamic> getFilterStats() {
    return {
      'current_user_id': _currentUserId,
      'has_current_location': _currentLocation != null,
      'cache_size': _filterCache.length,
      'last_cache_cleanup': _lastCacheCleanup.toIso8601String(),
    };
  }

  /// 打印过滤统计信息
  void printFilterStats() {
    final stats = getFilterStats();
    debugPrint('📊 [过滤统计] 当前用户: ${stats['current_user_id'] ?? '未设置'}');
    debugPrint(
      '📊 [过滤统计] 位置信息: ${stats['has_current_location'] ? '已设置' : '未设置'}',
    );
    debugPrint('📊 [过滤统计] 缓存大小: ${stats['cache_size']}');
  }

  /// 清理资源
  void dispose() {
    _clearFilterCache();
    debugPrint('🧹 [过滤服务] 资源已清理');
  }
}
